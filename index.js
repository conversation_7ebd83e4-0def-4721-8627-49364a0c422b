const axios = require('axios');
const opentok = require('opentok-jwt');  // OpenTok JWT library
const { GetObjectCommand, S3Client } = require('@aws-sdk/client-s3');
const { MongoClient } = require('mongodb');
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');

let cachedDb = null;
const API_KEY = process.env.API_KEY; // OpenTok API Key
const API_SECRET = process.env.API_SECRET; // OpenTok API Secret
const mongoUri = process.env.MONGODB_URI;
const region = process.env.AWS_S3_REGION;
const bucket = process.env.AWS_S3_BUCKET;
const awsAccessKey = process.env.AWS_ACCESSKEY_ID;
const awsSecretKey = process.env.AWS_SECRET_ACCESSKEY;
const deepgramApiKey = process.env.DEEPGRAM_API_KEY;

const s3Client = new S3Client({
  region, credentials: {
    accessKeyId: awsAccessKey,
    secretAccessKey: awsSecretKey,
  }
});

async function connectToDatabase(uri) {
  // Check if we have a connection already
  if (cachedDb) {
    return cachedDb;
  }

  try {
    console.log('Attempting to connect to MongoDB...');

    // Connect to MongoDB
    const client = await MongoClient.connect(uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000
    });

    // Select the database
    const db = client.db(process.env.MONGODB_DATABASE);

    console.log('Successfully connected to MongoDB');
    console.log('Connected Database:', process.env.MONGODB_DATABASE);

    // Cache the database connection
    cachedDb = db;

    return db;
  } catch (error) {
    console.error('Failed to connect to MongoDB:', error);
    throw error;
  }
}

const generateAWSPresignedURL = async ({ region, bucket, key }) => {
  try {
    const command = new GetObjectCommand({ Bucket: bucket, Key: key });
    const url = await getSignedUrl(s3Client, command, { expiresIn: 10 * 60 });
    console.log("AWS presigned URL generated successfully: ", url);
    return url;
  } catch (error) {
    console.log("Error generating AWS presigned URL: ", error);
    throw error;
  }
}

const createTranscribesUsingDeepgram = async (recordUrl) => {
  try {
    const response = await axios.post(
      "https://api.deepgram.com/v1/listen",
      {
        url: recordUrl
      },
      {
        headers: {
          Authorization: `Token ${deepgramApiKey}`,
        },
        params: {
          smart_format: true,
          model: "nova-3",
          diarize: true,
        },
      }
    );

    // Check for any errors in the response
    if (response.status !== 200 || !response.data) {
      throw new Error("Failed to transcribe audio");
    }

    return response.data; // This contains the transcription result
  } catch (error) {
    console.error("Error transcribing URL:", error.message || error);
    throw error;
  }
}

// Enhanced speaker detection for couples therapy
const detectSpeakersForCouplesTherapy = (conversationArray) => {
  const speakerStats = {};
  const speakerPhrases = {};

  // Analyze each speaker
  conversationArray.forEach((paragraph) => {
    const speakerId = paragraph.speaker;

    if (!speakerStats[speakerId]) {
      speakerStats[speakerId] = {
        count: 0,
        totalWords: 0,
        avgWordsPerSentence: 0,
        phrases: []
      };
      speakerPhrases[speakerId] = [];
    }

    speakerStats[speakerId].count += 1;

    // Collect phrases for analysis
    const sentences = paragraph.sentences.map(s => s.text).join(' ');
    speakerStats[speakerId].totalWords += sentences.split(' ').length;
    speakerPhrases[speakerId].push(sentences);
  });

  // Calculate averages and detect patterns
  Object.keys(speakerStats).forEach(speakerId => {
    const stats = speakerStats[speakerId];
    stats.avgWordsPerSentence = stats.totalWords / stats.count;
    stats.phrases = speakerPhrases[speakerId];

    // Analyze speech patterns to help identify roles
    const allText = stats.phrases.join(' ').toLowerCase();
    stats.hasTherapistLanguage = /\b(how do you feel|tell me about|what do you think|let's explore|therapy|session)\b/g.test(allText);
    stats.hasClientLanguage = /\b(i feel|my partner|we have|our relationship|i think|i want)\b/g.test(allText);
  });

  return {
    totalSpeakers: Object.keys(speakerStats).length,
    speakerStats,
    speakerPhrases
  };
}

// Process couples therapy transcript with enhanced speaker detection
const processCouplesTherapyTranscript = (conversationArray, meetingData) => {
  const speakerAnalysis = detectSpeakersForCouplesTherapy(conversationArray);
  const { totalSpeakers, speakerStats } = speakerAnalysis;

  console.log(`Detected ${totalSpeakers} speakers in couples therapy session`);

  // Create speaker metadata for therapist labeling
  const speakerMetadata = {};
  Object.keys(speakerStats).forEach(speakerId => {
    const stats = speakerStats[speakerId];
    speakerMetadata[speakerId] = {
      speakerId: parseInt(speakerId),
      utteranceCount: stats.count,
      totalWords: stats.totalWords,
      avgWordsPerSentence: stats.avgWordsPerSentence,
      suggestedRole: stats.hasTherapistLanguage ? 'THERAPIST' :
                    stats.hasClientLanguage ? 'CLIENT' : 'UNKNOWN',
      needsLabeling: true,
      assignedLabel: null, // To be set by therapist
      samplePhrases: stats.phrases.slice(0, 3) // First 3 phrases for context
    };
  });

  // Create structured transcript with speaker IDs (not roles yet)
  const structuredTranscript = [];
  conversationArray.forEach((paragraph) => {
    const sentences = paragraph.sentences.map(s => s.text).join(' ');
    structuredTranscript.push({
      speakerId: paragraph.speaker,
      text: sentences,
      timestamp: paragraph.sentences[0]?.start || 0,
      needsLabeling: true
    });
  });

  return {
    totalSpeakers,
    speakerMetadata,
    structuredTranscript,
    rawDeepgramData: conversationArray
  };
}

// Process regular therapy transcript (original logic)
const processRegularTherapyTranscript = (conversationArray, meetingData) => {
  const deepgramText = [];
  let speaker0Count = 0;
  let speaker1Count = 0;
  let speaker2Count = 0;
  let speaker0;
  let speaker1;
  const constructedSentences = [];

  // Count speakers
  conversationArray?.forEach((paragraph) => {
    if (paragraph?.speaker == 0) {
      speaker0Count += 1;
    }
    if (paragraph?.speaker == 1) {
      speaker1Count += 1;
    }
    if (paragraph?.speaker == 2) {
      speaker2Count += 1;
    }
  });

  // Determine speaker roles
  if (meetingData?.firstSpeaker?.role == 'THERAPIST') {
    speaker0 = 'THERAPIST';
    speaker1 = 'CLIENT';
  } else {
    speaker0 = 'CLIENT';
    speaker1 = 'THERAPIST';
  }

  // Process transcript based on speaker count
  if (speaker2Count == 0) {
    console.log(`Deepgram: There are 1 or 2 speakers in the session`);
    for (const conversation of conversationArray) {
      const sentences = conversation.sentences.map((sentence) => sentence.text).join(' ');
      deepgramText.push(`${conversation?.speaker} : ${sentences}`);

      if (conversation.speaker == 0) {
        constructedSentences.push(`${speaker0} : ${sentences}`)
      } else {
        constructedSentences.push(`${speaker1} : ${sentences}`)
      }
    }
  } else {
    console.log(`Deepgram: There are 3 speakers in the session. Filtering smallest speaker.`);
    let filteredConversationArray;

    // Find the smallest count among speakers and filter it out
    if (speaker0Count <= speaker1Count && speaker0Count <= speaker2Count) {
      filteredConversationArray = conversationArray.filter((paragraph) => paragraph.speaker !== 0);
      for (const conversation of filteredConversationArray) {
        const sentences = conversation.sentences.map((sentence) => sentence.text).join(' ');
        deepgramText.push(`${conversation?.speaker} : ${sentences}`);

        if (conversation.speaker == 1) {
          constructedSentences.push(`${speaker0} : ${sentences}`)
        } else {
          constructedSentences.push(`${speaker1} : ${sentences}`)
        }
      }
    } else if (speaker1Count <= speaker0Count && speaker1Count <= speaker2Count) {
      filteredConversationArray = conversationArray.filter((paragraph) => paragraph.speaker !== 1);
      for (const conversation of filteredConversationArray) {
        const sentences = conversation.sentences.map((sentence) => sentence.text).join(' ');
        deepgramText.push(`${conversation?.speaker} : ${sentences}`);

        if (conversation.speaker == 0) {
          constructedSentences.push(`${speaker0} : ${sentences}`)
        } else {
          constructedSentences.push(`${speaker1} : ${sentences}`)
        }
      }
    } else {
      filteredConversationArray = conversationArray.filter((paragraph) => paragraph.speaker !== 2);
      for (const conversation of filteredConversationArray) {
        const sentences = conversation.sentences.map((sentence) => sentence.text).join(' ');
        deepgramText.push(`${conversation?.speaker} : ${sentences}`);

        if (conversation.speaker == 0) {
          constructedSentences.push(`${speaker0} : ${sentences}`)
        } else {
          constructedSentences.push(`${speaker1} : ${sentences}`)
        }
      }
    }
  }

  return {
    totalSpeakers: speaker2Count > 0 ? 3 : (speaker1Count > 0 ? 2 : 1),
    speakerMetadata: {}, // Empty for regular sessions
    structuredTranscript: constructedSentences,
    rawDeepgramData: conversationArray
  };
}

// Main Lambda handler for couples therapy
exports.handler = async (event) => {
  const db = await connectToDatabase(mongoUri);
  const opentokUrl = 'https://api.opentok.com/v2/project';

  try {
    // Validate environment variables
    if (!API_KEY || !API_SECRET) {
      return {
        statusCode: 400,
        body: JSON.stringify({
          message: 'Environment variable did not found',
        }),
      };
    }

    // Validate S3 event structure
    if (!event.Records || !event.Records[0] || !event.Records[0].s3) {
      return {
        statusCode: 400,
        body: JSON.stringify({
          message: 'Invalid S3 event structure',
        }),
      };
    }

    const key = event.Records[0].s3.object.key;
    console.log('Processing couples therapy session - Key:', key);

    const archiveId = key.split('/')[1];
    console.log('Archive ID:', archiveId);

    // Generate JWT Token using OpenTok API key and secret
    const token = opentok.generateToken(API_KEY, API_SECRET);

    // Define the URL to fetch archive details
    const archiveUrl = `${opentokUrl}/${API_KEY}/archive/${archiveId}`;

    // Fetch archive data from OpenTok API
    const response = await axios.get(archiveUrl, {
      headers: {
        'X-OPENTOK-AUTH': token,
        'Content-Type': 'application/json',
      },
    });

    console.log('Archive data:', response.data);
    const archiveData = response.data;

    // Find meeting data
    const meetingCollection = db.collection('meetings');
    let preMeetingData;

    if (archiveData?.duration && archiveData?.duration !== null && archiveData?.duration > 0) {
      console.log('Duration found for session Id:', archiveData?.sessionId, ', Duration:', archiveData?.duration);
      preMeetingData = await meetingCollection.findOneAndUpdate(
        { meetingId: archiveData?.sessionId },
        { $set: { spentDuration: parseFloat((archiveData?.duration / 60).toFixed(2)) } },
        { returnOriginal: false }
      );
    } else {
      console.error(`Duration not found for session Id: ${archiveData?.sessionId}`);
    }

    if (!preMeetingData) {
      console.error(`Meeting not found for session Id: ${archiveData?.sessionId}`);
      return {
        statusCode: 500,
        body: JSON.stringify({
          message: `Meeting not found for session Id: ${archiveData?.sessionId}`,
        }),
      };
    }

    console.log('Meeting data found for ID:', preMeetingData._id);
    console.log('Participant count:', preMeetingData?.participantCount);

    // Check if this is a couples therapy session (3+ participants)
    const isCouplesTherapy = preMeetingData?.participantCount >= 3;
    console.log('Is couples therapy session:', isCouplesTherapy);

    // Generate presigned URL for audio file
    const clientAWSURL = await generateAWSPresignedURL({ region, bucket, key });
    console.log("clientAWSURL:", clientAWSURL);

    if (!clientAWSURL) {
      console.log(`Presigned url generating failed for archive Id: ${archiveId}`);
      return {
        statusCode: 500,
        body: JSON.stringify({
          message: `Presigned url generating failed for archive Id: ${archiveId}`,
        }),
      };
    }

    // Get transcription from Deepgram
    const transcribeList = await createTranscribesUsingDeepgram(clientAWSURL);
    if (!transcribeList) {
      console.log(`TranscribeList not found for archive Id: ${archiveId}`);
      return {
        statusCode: 500,
        body: JSON.stringify({
          message: `TranscribeList not found for archive Id: ${archiveId}`,
        }),
      };
    }

    const conversationArray = transcribeList?.results?.channels[0]?.alternatives[0]?.paragraphs?.paragraphs;

    if (!Array.isArray(conversationArray) || conversationArray.length === 0) {
      console.error("conversationArray is not an array or is null/undefined");
      return {
        statusCode: 500,
        body: JSON.stringify({
          message: 'Invalid conversation data from Deepgram',
        }),
      };
    }

    let transcriptData;

    if (isCouplesTherapy) {
      // Use enhanced couples therapy processing
      console.log('Processing as couples therapy session...');
      transcriptData = processCouplesTherapyTranscript(conversationArray, preMeetingData);
    } else {
      // Use standard processing for regular sessions
      console.log('Processing as regular therapy session...');
      // Fallback to original logic for non-couples sessions
      transcriptData = processRegularTherapyTranscript(conversationArray, preMeetingData);
    }

    // Prepare data for database storage
    const dataForTranscribe = {
      transcriptText: transcriptData.structuredTranscript,
      clientId: preMeetingData?.clientId,
      meetingId: preMeetingData?.meetingId,
      therapistId: preMeetingData?.therapistId,
      transCribeInProcess: false,
      speakersArray: [preMeetingData?.clientId, preMeetingData?.therapistId],
      meetingStartedTime: preMeetingData?.createdAt,
      videoUrl: "",

      // Enhanced couples therapy fields
      isCouplesTherapy: isCouplesTherapy,
      totalSpeakers: transcriptData.totalSpeakers,
      speakerMetadata: transcriptData.speakerMetadata,
      speakersDetected: true,
      needsSpeakerLabeling: isCouplesTherapy,
      speakerAssignments: {}, // To be filled by therapist

      // Original fields for compatibility
      deepgramText: conversationArray.map(conv => {
        const sentences = conv.sentences.map(s => s.text).join(' ');
        return `Speaker ${conv.speaker}: ${sentences}`;
      }),

      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Save to database
    const transcribeCollection = db.collection('transcribes');
    const transcribeTableRes = await transcribeCollection.insertOne(dataForTranscribe);

    if (transcribeTableRes.acknowledged) {
      console.log(`Couples therapy transcribe record created successfully. insertedId: ${transcribeTableRes.insertedId}`);

      // Update meeting record
      await meetingCollection.findOneAndUpdate(
        { _id: preMeetingData._id },
        {
          $set: {
            transcribeCreated: true,
            isCouplesTherapy: isCouplesTherapy,
            needsSpeakerLabeling: isCouplesTherapy
          }
        },
        { returnDocument: 'after' }
      );
    } else {
      console.log(`Transcribe record insert failed.`);
    }

    // Update meeting status if completed
    if (preMeetingData?.participantCount >= 2 && preMeetingData?.callingStatus == "CANCELLED" && archiveData?.duration > 0) {
      console.log(`Meeting completed - updating status. meetingId: ${preMeetingData._id}`);

      if (preMeetingData?.isAppointmentBased) {
        const appointmentCollection = db.collection('appointments');
        await appointmentCollection.findOneAndUpdate(
          { _id: preMeetingData.appointmentId },
          { $set: { status: "COMPLETED", meetingStatus: "COMPLETED" } },
          { returnOriginal: false }
        );
        await meetingCollection.findOneAndUpdate(
          { _id: preMeetingData._id },
          { $set: { callingStatus: "COMPLETED" } },
          { returnOriginal: false }
        );
        console.log(`Appointment and meeting status updated successfully! meetingId: ${preMeetingData._id}`);
      } else {
        await meetingCollection.findOneAndUpdate(
          { _id: preMeetingData._id },
          { $set: { callingStatus: "COMPLETED" } },
          { returnOriginal: false }
        );
        console.log(`Meeting status updated successfully! meetingId: ${preMeetingData._id}`);
      }
    }

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Couples therapy lambda function executed successfully',
        isCouplesTherapy: isCouplesTherapy,
        totalSpeakers: transcriptData.totalSpeakers,
        needsSpeakerLabeling: isCouplesTherapy
      }),
    };

  } catch (error) {
    console.error('Failed to run couples therapy lambda function:', error.message);
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: 'Failed to run couples therapy lambda function',
        error: error.message,
      }),
    };
  }
};